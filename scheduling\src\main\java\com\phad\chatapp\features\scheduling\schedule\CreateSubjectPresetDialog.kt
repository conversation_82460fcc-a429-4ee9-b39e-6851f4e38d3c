package com.phad.chatapp.features.scheduling.schedule

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.phad.chatapp.features.scheduling.models.SubjectPreset
import com.phad.chatapp.features.scheduling.models.SubjectConstants
import com.phad.chatapp.features.scheduling.ui.components.StandardButton
import com.phad.chatapp.features.scheduling.ui.theme.DarkBackground
import com.phad.chatapp.features.scheduling.ui.theme.NeutralCardSurface
import com.phad.chatapp.features.scheduling.ui.theme.YellowAccent
import com.google.firebase.firestore.FirebaseFirestore
import com.phad.chatapp.features.scheduling.firebase.FirestoreCollection
import kotlinx.coroutines.launch
import kotlinx.coroutines.tasks.await

@Composable
fun CreateSubjectPresetDialog(
    onDismiss: () -> Unit,
    onPresetCreated: (SubjectPreset) -> Unit
) {
    var presetName by remember { mutableStateOf("") }
    var subjectCounts by remember { mutableStateOf(
        SubjectConstants.ALL_SUBJECTS.associateWith { 0 }
    ) }
    var isLoading by remember { mutableStateOf(false) }
    var errorMessage by remember { mutableStateOf("") }
    
    val coroutineScope = rememberCoroutineScope()

    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(usePlatformDefaultWidth = false)
    ) {
        Card(
            modifier = Modifier
                .fillMaxWidth(0.95f)
                .fillMaxHeight(0.9f),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(containerColor = NeutralCardSurface)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(24.dp)
            ) {
                // Header
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "Create Subject Preset",
                        style = MaterialTheme.typography.titleLarge,
                        color = Color.White,
                        fontWeight = FontWeight.Bold
                    )
                    
                    IconButton(onClick = onDismiss) {
                        Icon(
                            Icons.Default.Close,
                            contentDescription = "Close",
                            tint = Color.White
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // Preset Name Input
                OutlinedTextField(
                    value = presetName,
                    onValueChange = { presetName = it },
                    label = { Text("Preset Name", color = Color(0xFFB0B0B0)) },
                    modifier = Modifier.fillMaxWidth(),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedTextColor = Color.White,
                        unfocusedTextColor = Color.White,
                        focusedBorderColor = YellowAccent,
                        unfocusedBorderColor = Color(0xFF666666)
                    )
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // Error message
                if (errorMessage.isNotEmpty()) {
                    Text(
                        text = errorMessage,
                        color = Color.Red,
                        style = MaterialTheme.typography.bodySmall,
                        modifier = Modifier.padding(bottom = 8.dp)
                    )
                }
                
                // Subjects List
                Text(
                    text = "Subject Classes",
                    style = MaterialTheme.typography.titleMedium,
                    color = Color.White,
                    fontWeight = FontWeight.Medium,
                    modifier = Modifier.padding(bottom = 12.dp)
                )
                
                LazyColumn(
                    modifier = Modifier.weight(1f),
                    verticalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    items(SubjectConstants.ALL_SUBJECTS) { subjectCode ->
                        SubjectCountRow(
                            subjectCode = subjectCode,
                            subjectName = SubjectConstants.SUBJECT_NAMES[subjectCode] ?: subjectCode,
                            count = subjectCounts[subjectCode] ?: 0,
                            onCountChange = { newCount ->
                                subjectCounts = subjectCounts.toMutableMap().apply {
                                    this[subjectCode] = newCount
                                }
                            }
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // Action Buttons
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    OutlinedButton(
                        onClick = onDismiss,
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.outlinedButtonColors(
                            contentColor = Color.White
                        )
                    ) {
                        Text("Cancel")
                    }
                    
                    StandardButton(
                        onClick = {
                            if (presetName.isBlank()) {
                                errorMessage = "Please enter a preset name"
                                return@StandardButton
                            }

                            // Check for invalid characters in preset name (since it will be used as document ID)
                            if (presetName.contains("/") || presetName.contains("\\") || presetName.contains(".")) {
                                errorMessage = "Preset name cannot contain / \\ or . characters"
                                return@StandardButton
                            }

                            val totalClasses = subjectCounts.values.sum()
                            if (totalClasses == 0) {
                                errorMessage = "Please add at least one subject class"
                                return@StandardButton
                            }
                            
                            isLoading = true
                            errorMessage = ""
                            
                            coroutineScope.launch {
                                try {
                                    val preset = SubjectPreset(
                                        name = presetName,
                                        subjects = subjectCounts.filterValues { it > 0 }
                                    )

                                    val savedPreset = saveSubjectPreset(preset)
                                    onPresetCreated(savedPreset)
                                } catch (e: Exception) {
                                    errorMessage = e.message ?: "Failed to save preset"
                                    isLoading = false
                                }
                            }
                        },
                        enabled = !isLoading,
                        modifier = Modifier.weight(1f)
                    ) {
                        if (isLoading) {
                            CircularProgressIndicator(
                                modifier = Modifier.size(16.dp),
                                color = Color.Black,
                                strokeWidth = 2.dp
                            )
                        } else {
                            Text(
                                "Save Preset",
                                color = Color.Black,
                                fontWeight = FontWeight.Medium
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun SubjectCountRow(
    subjectCode: String,
    subjectName: String,
    count: Int,
    onCountChange: (Int) -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .background(
                color = DarkBackground,
                shape = RoundedCornerShape(12.dp)
            )
            .padding(16.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        // Subject Info
        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = subjectName,
                style = MaterialTheme.typography.bodyLarge,
                color = Color.White,
                fontWeight = FontWeight.Medium
            )
            Text(
                text = subjectCode,
                style = MaterialTheme.typography.bodySmall,
                color = Color(0xFFB0B0B0)
            )
        }
        
        // Count Controls
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Decrement Button
            Box(
                modifier = Modifier
                    .size(40.dp)
                    .shadow(
                        elevation = 2.dp,
                        shape = CircleShape,
                        spotColor = YellowAccent.copy(alpha = 0.5f)
                    )
                    .background(
                        color = if (count > 0) YellowAccent else Color(0xFF666666),
                        shape = CircleShape
                    )
                    .clickable(enabled = count > 0) {
                        onCountChange((count - 1).coerceAtLeast(0))
                    },
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "−",
                    fontWeight = FontWeight.Bold,
                    fontSize = 18.sp,
                    color = if (count > 0) Color.Black else Color(0xFFB0B0B0)
                )
            }
            
            // Count Display
            Text(
                text = count.toString(),
                style = MaterialTheme.typography.titleMedium,
                color = Color.White,
                fontWeight = FontWeight.Bold,
                textAlign = TextAlign.Center,
                modifier = Modifier.widthIn(min = 32.dp)
            )
            
            // Increment Button
            Box(
                modifier = Modifier
                    .size(40.dp)
                    .shadow(
                        elevation = 2.dp,
                        shape = CircleShape,
                        spotColor = YellowAccent.copy(alpha = 0.5f)
                    )
                    .background(
                        color = YellowAccent,
                        shape = CircleShape
                    )
                    .clickable {
                        onCountChange(count + 1)
                    },
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "+",
                    fontWeight = FontWeight.Bold,
                    fontSize = 18.sp,
                    color = Color.Black
                )
            }
        }
    }
}

private suspend fun saveSubjectPreset(preset: SubjectPreset): SubjectPreset {
    val db = FirebaseFirestore.getInstance()
    // Use preset name as document ID for better organization
    val docRef = db.collection(FirestoreCollection.SUBJECT_PRESETS).document(preset.name)

    // Check if document already exists
    val existingDoc = docRef.get().await()
    if (existingDoc.exists()) {
        throw Exception("A preset with the name '${preset.name}' already exists")
    }

    // Create preset data without storing the id field in the document
    val presetData = mapOf(
        "name" to preset.name,
        "subjects" to preset.subjects,
        "createdAt" to System.currentTimeMillis()
    )

    docRef.set(presetData).await()

    // Return preset with document ID for local state management
    return preset.copy(
        id = preset.name,
        createdAt = System.currentTimeMillis()
    )
}
