package com.phad.chatapp.features.scheduling.schedule

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Add
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.navigation.NavController
import com.phad.chatapp.features.scheduling.models.SubjectPreset
import com.phad.chatapp.features.scheduling.ui.components.StandardButton
import com.phad.chatapp.features.scheduling.ui.theme.DarkBackground
import com.phad.chatapp.features.scheduling.ui.theme.NeutralCardSurface
import com.phad.chatapp.features.scheduling.ui.theme.YellowAccent
import com.google.firebase.firestore.FirebaseFirestore
import com.phad.chatapp.features.scheduling.firebase.FirestoreCollection
import kotlinx.coroutines.tasks.await

@Composable
fun SubjectAssignmentScreen(navController: NavController) {
    var subjectPresets by remember { mutableStateOf<List<SubjectPreset>>(emptyList()) }
    var isLoading by remember { mutableStateOf(true) }
    var showCreateDialog by remember { mutableStateOf(false) }

    // Load subject presets from Firebase
    LaunchedEffect(Unit) {
        loadSubjectPresets { presets ->
            subjectPresets = presets
            isLoading = false
        }
    }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(DarkBackground)
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 20.dp, vertical = 8.dp)
        ) {
            // Header with back button and title
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                IconButton(
                    onClick = { navController.navigateUp() },
                    modifier = Modifier.size(48.dp)
                ) {
                    Icon(
                        Icons.AutoMirrored.Filled.ArrowBack,
                        contentDescription = "Back",
                        tint = Color.White
                    )
                }
                
                Text(
                    text = "Subject Assignment",
                    style = MaterialTheme.typography.titleLarge,
                    color = Color.White,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier
                        .weight(1f)
                        .padding(start = 8.dp)
                )
                
                StandardButton(
                    onClick = { navController.navigate("subjectPresetSelection") }
                ) {
                    Text(
                        "Pair",
                        color = Color.Black,
                        fontWeight = FontWeight.Medium
                    )
                }
            }

            if (isLoading) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator(color = YellowAccent)
                }
            } else {
                Column {
                    // Create Preset Button (directly below header)
                    StandardButton(
                        onClick = { showCreateDialog = true },
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(bottom = 16.dp)
                    ) {
                        Text(
                            "Create Preset",
                            color = Color.Black,
                            fontWeight = FontWeight.Medium
                        )
                    }

                    // Content
                    LazyColumn(
                        verticalArrangement = Arrangement.spacedBy(16.dp),
                        contentPadding = PaddingValues(vertical = 16.dp)
                    ) {

                        // Existing Subject Presets
                        if (subjectPresets.isNotEmpty()) {
                            item {
                                Text(
                                    "Existing Subject Presets",
                                    style = MaterialTheme.typography.titleMedium,
                                    color = Color.White,
                                    fontWeight = FontWeight.Medium,
                                    modifier = Modifier.padding(vertical = 8.dp)
                                )
                            }

                            items(subjectPresets) { preset ->
                                SubjectPresetCard(
                                    preset = preset,
                                    onClick = { /* TODO: Edit preset */ }
                                )
                            }
                        }
                    }
                }
            }
        }
    }

    // Create Subject Preset Dialog
    if (showCreateDialog) {
        CreateSubjectPresetDialog(
            onDismiss = { showCreateDialog = false },
            onPresetCreated = { newPreset ->
                subjectPresets = subjectPresets + newPreset
                showCreateDialog = false
            }
        )
    }
}

@Composable
fun SubjectPresetCard(
    preset: SubjectPreset,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(containerColor = NeutralCardSurface)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Text(
                text = preset.name,
                style = MaterialTheme.typography.titleSmall,
                color = Color.White,
                fontWeight = FontWeight.Medium
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            val totalClasses = preset.subjects.values.sum()
            Text(
                text = "$totalClasses total classes",
                style = MaterialTheme.typography.bodySmall,
                color = Color(0xFFB0B0B0)
            )
        }
    }
}

private suspend fun loadSubjectPresets(onResult: (List<SubjectPreset>) -> Unit) {
    try {
        val db = FirebaseFirestore.getInstance()
        val snapshot = db.collection(FirestoreCollection.SUBJECT_PRESETS).get().await()
        val presets = snapshot.documents.mapNotNull { doc ->
            doc.toObject(SubjectPreset::class.java)?.copy(id = doc.id)
        }
        onResult(presets)
    } catch (e: Exception) {
        onResult(emptyList())
    }
}
