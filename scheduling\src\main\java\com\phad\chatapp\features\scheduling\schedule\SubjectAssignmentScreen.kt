package com.phad.chatapp.features.scheduling.schedule

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.navigation.NavController
import com.phad.chatapp.features.scheduling.models.SubjectPreset
import com.phad.chatapp.features.scheduling.ui.components.StandardButton
import com.phad.chatapp.features.scheduling.ui.theme.DarkBackground
import com.phad.chatapp.features.scheduling.ui.theme.DarkSurface
import com.phad.chatapp.features.scheduling.ui.theme.ErrorRed
import com.phad.chatapp.features.scheduling.ui.theme.NeutralCardSurface
import com.phad.chatapp.features.scheduling.ui.theme.YellowAccent
import com.google.firebase.firestore.FirebaseFirestore
import com.phad.chatapp.features.scheduling.firebase.FirestoreCollection
import kotlinx.coroutines.launch
import kotlinx.coroutines.tasks.await

@Composable
fun SubjectAssignmentScreen(navController: NavController) {
    var subjectPresets by remember { mutableStateOf<List<SubjectPreset>>(emptyList()) }
    var isLoading by remember { mutableStateOf(true) }
    var showCreateDialog by remember { mutableStateOf(false) }
    var showDeleteDialog by remember { mutableStateOf(false) }
    var presetToDelete by remember { mutableStateOf<SubjectPreset?>(null) }
    var isDeleting by remember { mutableStateOf(false) }

    val snackbarHostState = remember { SnackbarHostState() }
    val coroutineScope = rememberCoroutineScope()

    // Load subject presets from Firebase
    LaunchedEffect(Unit) {
        loadSubjectPresets { presets ->
            subjectPresets = presets
            isLoading = false
        }
    }

    // Function to delete a preset
    fun deletePreset(preset: SubjectPreset) {
        isDeleting = true

        coroutineScope.launch {
            try {
                val success = deleteSubjectPreset(preset.name)
                if (success) {
                    // Remove from local list
                    subjectPresets = subjectPresets.filter { it.name != preset.name }
                    snackbarHostState.showSnackbar("Preset '${preset.name}' deleted successfully")
                } else {
                    snackbarHostState.showSnackbar("Failed to delete preset")
                }
            } catch (e: Exception) {
                snackbarHostState.showSnackbar("Error deleting preset: ${e.message}")
            } finally {
                isDeleting = false
            }
        }
    }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(DarkBackground)
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 20.dp, vertical = 8.dp)
        ) {
            // Header with back button and title
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                IconButton(
                    onClick = { navController.navigateUp() },
                    modifier = Modifier.size(48.dp)
                ) {
                    Icon(
                        Icons.AutoMirrored.Filled.ArrowBack,
                        contentDescription = "Back",
                        tint = Color.White
                    )
                }
                
                Text(
                    text = "Subject Presets",
                    style = MaterialTheme.typography.titleLarge,
                    color = Color.White,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier
                        .weight(1f)
                        .padding(start = 8.dp)
                )

                // Create Preset Button in header
                StandardButton(
                    onClick = { showCreateDialog = true },
                    modifier = Modifier.padding(end = 8.dp)
                ) {
                    Text(
                        "Create Preset",
                        color = Color.Black,
                        fontWeight = FontWeight.Medium
                    )
                }

                StandardButton(
                    onClick = { navController.navigate("subjectPresetSelection") }
                ) {
                    Text(
                        "Pair",
                        color = Color.Black,
                        fontWeight = FontWeight.Medium
                    )
                }
            }

            if (isLoading) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator(color = YellowAccent)
                }
            } else {
                // Content starts immediately after header
                LazyColumn(
                    verticalArrangement = Arrangement.spacedBy(16.dp),
                    contentPadding = PaddingValues(vertical = 16.dp)
                ) {
                    // Existing Subject Presets
                    if (subjectPresets.isNotEmpty()) {
                        item {
                            Text(
                                "Existing Subject Presets",
                                style = MaterialTheme.typography.titleMedium,
                                color = Color.White,
                                fontWeight = FontWeight.Medium,
                                modifier = Modifier.padding(vertical = 8.dp)
                            )
                        }

                        items(subjectPresets) { preset ->
                            SubjectPresetCard(
                                preset = preset,
                                onClick = { /* TODO: Edit preset */ },
                                onDelete = {
                                    presetToDelete = preset
                                    showDeleteDialog = true
                                }
                            )
                        }
                    }
                }
            }
        }

        // SnackbarHost for user feedback
        SnackbarHost(
            hostState = snackbarHostState,
            modifier = Modifier.align(Alignment.BottomCenter)
        )
    }

    // Create Subject Preset Dialog
    if (showCreateDialog) {
        CreateSubjectPresetDialog(
            onDismiss = { showCreateDialog = false },
            onPresetCreated = { newPreset ->
                subjectPresets = subjectPresets + newPreset
                showCreateDialog = false
                coroutineScope.launch {
                    snackbarHostState.showSnackbar("Preset '${newPreset.name}' created successfully")
                }
            }
        )
    }

    // Delete confirmation dialog
    if (showDeleteDialog && presetToDelete != null) {
        AlertDialog(
            onDismissRequest = {
                showDeleteDialog = false
                presetToDelete = null
            },
            title = {
                Text(
                    "Delete Preset",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    color = Color.White
                )
            },
            containerColor = DarkSurface,
            titleContentColor = Color.White,
            textContentColor = Color.White,
            shape = RoundedCornerShape(16.dp),
            text = {
                Text(
                    "Are you sure you want to delete '${presetToDelete!!.name}'? This action cannot be undone.",
                    style = MaterialTheme.typography.bodyMedium,
                    color = Color(0xFFB0B0B0)
                )
            },
            confirmButton = {
                Button(
                    onClick = {
                        deletePreset(presetToDelete!!)
                        showDeleteDialog = false
                        presetToDelete = null
                    },
                    enabled = !isDeleting,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = ErrorRed
                    ),
                    shape = RoundedCornerShape(8.dp)
                ) {
                    if (isDeleting) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(16.dp),
                            color = Color.White,
                            strokeWidth = 2.dp
                        )
                    } else {
                        Text(
                            "Delete",
                            color = Color.White,
                            fontWeight = FontWeight.Medium
                        )
                    }
                }
            },
            dismissButton = {
                TextButton(
                    onClick = {
                        showDeleteDialog = false
                        presetToDelete = null
                    },
                    enabled = !isDeleting
                ) {
                    Text("Cancel", color = YellowAccent)
                }
            }
        )
    }
}

@Composable
fun SubjectPresetCard(
    preset: SubjectPreset,
    onClick: () -> Unit,
    onDelete: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(containerColor = NeutralCardSurface)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Content section
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = preset.name,
                    style = MaterialTheme.typography.titleSmall,
                    color = Color.White,
                    fontWeight = FontWeight.Medium
                )

                Spacer(modifier = Modifier.height(8.dp))

                val totalClasses = preset.subjects.values.sum()
                Text(
                    text = "$totalClasses total classes",
                    style = MaterialTheme.typography.bodySmall,
                    color = Color(0xFFB0B0B0)
                )
            }

            // Delete button following UI.md specifications
            IconButton(
                onClick = onDelete,
                modifier = Modifier
                    .size(36.dp)
                    .background(
                        color = ErrorRed.copy(alpha = 0.2f),
                        shape = CircleShape
                    )
            ) {
                Icon(
                    imageVector = Icons.Default.Delete,
                    contentDescription = "Delete preset",
                    tint = ErrorRed,
                    modifier = Modifier.size(18.dp)
                )
            }
        }
    }
}

private suspend fun loadSubjectPresets(onResult: (List<SubjectPreset>) -> Unit) {
    try {
        val db = FirebaseFirestore.getInstance()
        val snapshot = db.collection(FirestoreCollection.SUBJECT_PRESETS).get().await()
        val presets = snapshot.documents.mapNotNull { doc ->
            try {
                val data = doc.data
                if (data != null) {
                    SubjectPreset(
                        id = doc.id, // Document ID is the preset name
                        name = data["name"] as? String ?: doc.id,
                        subjects = (data["subjects"] as? Map<String, Any>)?.mapValues {
                            (it.value as? Number)?.toInt() ?: 0
                        } ?: emptyMap(),
                        createdAt = 0L // No longer stored in database
                    )
                } else null
            } catch (e: Exception) {
                null
            }
        }
        onResult(presets)
    } catch (e: Exception) {
        onResult(emptyList())
    }
}

private suspend fun deleteSubjectPreset(presetName: String): Boolean {
    return try {
        val db = FirebaseFirestore.getInstance()
        // Use preset name as document ID
        db.collection(FirestoreCollection.SUBJECT_PRESETS)
            .document(presetName)
            .delete()
            .await()
        true
    } catch (e: Exception) {
        false
    }
}
